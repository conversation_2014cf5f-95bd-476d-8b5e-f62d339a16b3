<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted } from "vue";

defineOptions({
  name: "Solutions",
});

// 页面状态
const isLoaded = ref(false);

// 行业痛点数据
const industryPainPoints = ref([
  {
    icon: "mdi:water-alert",
    title: "水资源过度开发",
    description:
      "对水资源过度、无序的开发利用，造成河湖生态失衡，引发水质恶化、岸线崩塌、水体萎缩、洪水泛滥等问题",
  },
  {
    icon: "mdi:map-marker-path",
    title: "流域系统复杂",
    description:
      "流域包含复杂的水文演变、物质输移和人类活动等过程，相互影响，使得河湖问题的诊断、溯源和治理面临挑战",
  },
  {
    icon: "mdi:alert-circle",
    title: "灾害威胁严重",
    description:
      "暴雨、洪水、干旱、污染等河湖问题严重威胁人民生命财产安全，舆情传播迅速，加剧负面影响",
  },
  {
    icon: "mdi:chart-line-variant",
    title: "治理能力不足",
    description:
      "传统治理方式难以应对复杂的流域问题，缺乏智能化手段和科学决策支撑",
  },
]);

// 核心解决方案数据 - 基于真实的人工智能+水利解决方案
const coreSolutions = ref([
  {
    icon: "mdi:brain",
    category: "流域智能体",
    title: "人工智能+水利解决方案",
    description:
      "通过整合空天地水工等多源感知数据，依托行业机理模型、人工智能模型、三维空间模型等，构建与现实映射的流域数字孪生，生成流域智能体",
    benefits: [
      "持续提升感知能力",
      "增强认知决策能力",
      "优化行动执行能力",
      "辅助任务处置决策",
    ],
    technologies: [
      "多源感知数据",
      "行业机理模型",
      "人工智能模型",
      "三维空间模型",
    ],
    projectCount: 50,
    clientCount: 25,
  },
]);

// 方案特点数据
const solutionFeatures = ref([
  {
    icon: "mdi:target",
    title: "目标驱动",
    description:
      "以流域或区域水资源开发利用、防洪减灾等目标驱动流域智能体进行目标分解和任务规划，结合知识分析和模型推理，给出业务场景的最优决策方案",
    color: "var(--primary-blue)",
  },
  {
    icon: "mdi:account-group",
    title: "群体智能",
    description:
      "以流域作为一个宏观智能体，集合了流域内的工程、测站、设备、管理单元等具体智能体，各智能体基于网络相互联动，形成群体智能",
    color: "var(--electric-blue)",
  },
  {
    icon: "mdi:account-tie",
    title: "人机协同",
    description:
      "人类专家与流域智能体协同工作，负责监督智能体的学习训练，对任务处置结果进行复核确认，根据推荐方案进行决策指挥等",
    color: "var(--neon-cyan)",
  },
  {
    icon: "mdi:merge",
    title: "虚实融合",
    description:
      "流域智能体通过流域数字孪生模拟和演练工作技能，在参与业务管理活动和对物理流域的感知、交互过程中持续积累知识和优化模型",
    color: "var(--success-green)",
  },
]);

// 应用场景数据
const applicationScenarios = ref([
  {
    icon: "mdi:shield-alert",
    title: "水旱灾害防御",
    description: "基于流域智能体的洪水预警预报和应急响应决策支持",
    color: "var(--warning-orange)",
  },
  {
    icon: "mdi:water-pump",
    title: "水资源配置",
    description: "智能化水资源调度和优化配置，提升水资源利用效率",
    color: "var(--primary-blue)",
  },
  {
    icon: "mdi:leaf",
    title: "水环境治理",
    description: "水环境质量监测分析和污染治理决策支持",
    color: "var(--success-green)",
  },
  {
    icon: "mdi:nature",
    title: "水生态保护",
    description: "水生态系统健康评估和保护修复指导",
    color: "var(--neon-cyan)",
  },
]);

// 事件处理
const handleSolutionDetail = (solution) => {
  console.log("查看解决方案详情:", solution);
};

const handleConsultation = () => {
  console.log("预约专家咨询");
};

const handleDemo = () => {
  console.log("观看演示视频");
};

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);
});
</script>

<template>
  <div class="solutions" :class="{ loaded: isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 15" :key="i" :class="`particle particle-${i}`"></div>
        </div>
        <div class="hero-network">
          <div v-for="i in 8" :key="i" :class="`network-node node-${i}`"></div>
          <svg class="network-lines" viewBox="0 0 100 100">
            <path class="network-path" d="M20,30 Q50,10 80,30" />
            <path class="network-path" d="M15,60 Q40,40 65,60" />
            <path class="network-path" d="M30,80 Q60,60 90,80" />
          </svg>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-badge fade-in-up">
            <Icon icon="mdi:lightbulb-on-outline" class="badge-icon" />
            <span>智慧水利数字化转型</span>
          </div>

          <h1 class="hero-title fade-in-up delay-1">
            <span class="title-line">人工智能+水利</span>
            <span class="title-line title-highlight">解决方案</span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            通过整合空天地水工等多源感知数据，构建与现实映射的<strong>流域数字孪生</strong>，<br />
            生成流域智能体，显著提升流域治理能力
          </p>

          <div class="hero-features fade-in-up delay-3">
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:chart-timeline-variant" />
              </div>
              <div class="feature-text">
                <div class="feature-title">实时监控</div>
                <div class="feature-desc">7×24小时全天候监测</div>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:brain" />
              </div>
              <div class="feature-text">
                <div class="feature-title">智能分析</div>
                <div class="feature-desc">AI驱动的预测分析</div>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:shield-check" />
              </div>
              <div class="feature-text">
                <div class="feature-title">安全可靠</div>
                <div class="feature-desc">企业级安全保障</div>
              </div>
            </div>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-4">
          <div class="visual-container">
            <div class="solution-hub">
              <div class="hub-center">
                <Icon icon="mdi:water-circle" class="hub-icon" />
                <div class="hub-pulse"></div>
              </div>
              <div class="hub-orbits">
                <div class="orbit orbit-1">
                  <div class="orbit-item item-1">
                    <Icon icon="mdi:chart-line" />
                  </div>
                </div>
                <div class="orbit orbit-2">
                  <div class="orbit-item item-2">
                    <Icon icon="mdi:cloud-outline" />
                  </div>
                  <div class="orbit-item item-3">
                    <Icon icon="mdi:shield-check" />
                  </div>
                </div>
                <div class="orbit orbit-3">
                  <div class="orbit-item item-4">
                    <Icon icon="mdi:trending-up" />
                  </div>
                  <div class="orbit-item item-5">
                    <Icon icon="mdi:database" />
                  </div>
                  <div class="orbit-item item-6">
                    <Icon icon="mdi:cog" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 行业痛点 -->
    <section class="industry-pain-points">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:alert-circle" />
            <span>行业痛点</span>
          </div>
          <h2 class="section-title">当前面临的挑战</h2>
          <p class="section-subtitle">
            河湖问题在水里，根源在岸上，需要水岸同治，从流域角度实施系统治理
          </p>
        </div>

        <div class="pain-points-grid">
          <div
            v-for="(point, index) in industryPainPoints"
            :key="index"
            class="pain-point-card"
          >
            <div class="point-icon">
              <Icon :icon="point.icon" />
            </div>
            <h3 class="point-title">{{ point.title }}</h3>
            <p class="point-description">{{ point.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心解决方案 -->
    <section class="core-solutions">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:brain" />
            <span>核心方案</span>
          </div>
          <h2 class="section-title">流域智能体解决方案</h2>
          <p class="section-subtitle">
            嵌入水旱灾害防御、水资源配置、水环境治理、水生态保护等业务应用
          </p>
        </div>

        <div class="solutions-grid">
          <div
            v-for="(solution, index) in coreSolutions"
            :key="index"
            class="solution-card"
            @click="handleSolutionDetail(solution)"
          >
            <div class="card-header">
              <div class="solution-icon">
                <Icon :icon="solution.icon" />
              </div>
              <div class="solution-category">{{ solution.category }}</div>
            </div>

            <div class="card-content">
              <h3 class="solution-title">{{ solution.title }}</h3>
              <p class="solution-description">{{ solution.description }}</p>

              <div class="solution-benefits">
                <div class="benefits-title">核心价值</div>
                <div class="benefits-list">
                  <div
                    class="benefit"
                    v-for="benefit in solution.benefits"
                    :key="benefit"
                  >
                    <Icon icon="mdi:check-circle" class="benefit-icon" />
                    <span>{{ benefit }}</span>
                  </div>
                </div>
              </div>

              <div class="solution-tech">
                <div class="tech-title">技术栈</div>
                <div class="tech-tags">
                  <span
                    class="tech-tag"
                    v-for="tech in solution.technologies"
                    :key="tech"
                    >{{ tech }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 方案特点 -->
    <section class="solution-features">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:star-four-points" />
            <span>方案特点</span>
          </div>
          <h2 class="section-title">四大核心特点</h2>
          <p class="section-subtitle">
            目标驱动、群体智能、人机协同、虚实融合的创新解决方案
          </p>
        </div>

        <div class="features-grid">
          <div
            v-for="(feature, index) in solutionFeatures"
            :key="index"
            class="feature-card"
            :style="{ '--feature-color': feature.color }"
          >
            <div class="feature-icon">
              <Icon :icon="feature.icon" />
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 应用场景 -->
    <section class="application-scenarios">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:application" />
            <span>应用场景</span>
          </div>
          <h2 class="section-title">业务应用领域</h2>
          <p class="section-subtitle">
            嵌入水利行业核心业务场景，提供智能化决策支持
          </p>
        </div>

        <div class="scenarios-grid">
          <div
            v-for="(scenario, index) in applicationScenarios"
            :key="index"
            class="scenario-card"
            :style="{ '--scenario-color': scenario.color }"
          >
            <div class="scenario-icon">
              <Icon :icon="scenario.icon" />
            </div>
            <h3 class="scenario-title">{{ scenario.title }}</h3>
            <p class="scenario-description">{{ scenario.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-grid">
          <div v-for="i in 30" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <Icon icon="mdi:rocket-launch" />
              <span>开始您的数字化之旅</span>
            </div>
            <h2 class="cta-title">开启流域智能化治理新时代</h2>
            <p class="cta-subtitle">
              我们的专业团队将为您提供<strong>流域数字孪生构建</strong>和<strong
                >智能体定制开发</strong
              ><br />
              以水利现代化推动人与自然和谐共生
            </p>

            <div class="cta-benefits">
              <div class="benefit-item">
                <Icon icon="mdi:account-tie" />
                <span>专家1对1咨询</span>
              </div>
              <div class="benefit-item">
                <Icon icon="mdi:file-document-outline" />
                <span>免费方案评估</span>
              </div>
              <div class="benefit-item">
                <Icon icon="mdi:clock-fast" />
                <span>快速响应服务</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@import url("@/assets/styles/solutions.scss");
</style>
