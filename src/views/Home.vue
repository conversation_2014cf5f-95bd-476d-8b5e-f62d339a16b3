<template>
  <div class="home" :class="{ loaded: isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 6" :key="i" :class="`particle particle-${i}`"></div>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">
            <span class="title-line">
              <img
                src="@/assets/images/home-title-left.webp"
                alt="智慧水生态"
                class="title-image fade-in-up"
              />
            </span>
            <span class="title-line">
              <img
                src="@/assets/images/home-title-right.webp"
                alt="数智新未来"
                class="title-image fade-in-up delay-1"
              />
            </span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            基于物联网、大数据、AI等前沿技术，为水利行业提供<br />
            <strong>全方位数字化转型解决方案</strong>
          </p>

          <div class="hero-stats fade-in-up delay-3">
            <div class="stat-item">
              <div class="stat-number">500+</div>
              <div class="stat-label">成功项目</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-number">50+</div>
              <div class="stat-label">合作伙伴</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-number">15年</div>
              <div class="stat-label">行业经验</div>
            </div>
          </div>

          <div class="hero-actions fade-in-up delay-4">
            <button class="btn-primary" @click="handleExplore">
              <Icon icon="mdi:rocket-launch" class="btn-icon" />
              <span>探索解决方案</span>
              <Icon icon="mdi:arrow-right" class="btn-arrow" />
            </button>
            <button class="btn-secondary" @click="handleContact">
              <Icon icon="mdi:phone" class="btn-icon" />
              <span>联系我们</span>
            </button>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-5">
          <div class="visual-container">
            <div class="tech-rings">
              <div class="ring ring-1"></div>
              <div class="ring ring-2"></div>
              <div class="ring ring-3"></div>
            </div>
            <div class="center-logo">
              <Icon icon="mdi:water-circle" class="logo-icon" />
            </div>
            <div class="floating-icons">
              <div class="floating-icon icon-1">
                <Icon icon="mdi:chart-line" />
              </div>
              <div class="floating-icon icon-2">
                <Icon icon="mdi:cloud-outline" />
              </div>
              <div class="floating-icon icon-3">
                <Icon icon="mdi:shield-check" />
              </div>
              <div class="floating-icon icon-4">
                <Icon icon="mdi:trending-up" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="scroll-indicator">
        <div class="scroll-dot"></div>
        <div class="scroll-text">滚动了解更多</div>
      </div>
    </section>

    <!-- 核心优势 -->
    <section class="advantages-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">为什么选择怀川科技</h2>
          <p class="section-subtitle">专业实力 × 创新技术 × 贴心服务</p>
        </div>

        <div class="advantages-grid">
          <div
            class="advantage-card"
            v-for="(advantage, index) in advantages"
            :key="index"
          >
            <div class="card-icon">
              <Icon :icon="advantage.icon" />
            </div>
            <h3 class="card-title">{{ advantage.title }}</h3>
            <p class="card-description">{{ advantage.description }}</p>
            <div class="card-features">
              <div
                class="feature"
                v-for="feature in advantage.features"
                :key="feature"
              >
                <Icon icon="mdi:check-circle" class="feature-icon" />
                <span>{{ feature }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品展示 -->
    <section class="products-preview">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">核心产品</h2>
          <p class="section-subtitle">覆盖水利管理全生命周期的智能化产品矩阵</p>
        </div>

        <div class="products-showcase">
          <div
            class="product-card featured"
            v-for="(product, index) in featuredProducts"
            :key="index"
          >
            <div class="product-visual">
              <div class="product-icon">
                <Icon :icon="product.icon" />
              </div>
              <div class="product-badge">{{ product.badge }}</div>
            </div>
            <div class="product-content">
              <h3 class="product-title">{{ product.title }}</h3>
              <p class="product-description">{{ product.description }}</p>
              <div class="product-tags">
                <span class="tag" v-for="tag in product.tags" :key="tag">{{
                  tag
                }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="section-footer">
          <button class="btn-outline" @click="handleProducts">
            <span>查看全部产品</span>
            <Icon icon="mdi:arrow-right" class="btn-arrow" />
          </button>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-grid">
          <div v-for="i in 20" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <h2 class="cta-title">准备开始您的数字化转型之旅？</h2>
            <p class="cta-subtitle">
              让我们的专家团队为您量身定制最适合的智慧水利解决方案
            </p>
          </div>
          <div class="cta-actions">
            <div class="contact-info">
              <div class="contact-item">
                <Icon icon="mdi:phone" />
                <span>186-1032-4200</span>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:email" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们弹窗 -->
    <ContactModal v-model="showContactModal" />
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { Icon } from "@iconify/vue";
import { onMounted, ref } from "vue";
import ContactModal from "@/components/ContactModal.vue";

defineOptions({
  name: "Home",
});

const router = useRouter();
const isLoaded = ref(false);
const showContactModal = ref(false);

// 页面数据
const advantages = ref([
  {
    icon: "mdi:lightbulb-on",
    title: "技术创新",
    description: "持续投入研发，掌握核心技术",
    features: ["自主知识产权", "前沿技术栈", "持续创新"],
  },
  {
    icon: "mdi:account-group",
    title: "专业团队",
    description: "15年行业经验的专家团队",
    features: ["资深专家", "专业服务", "技术支持"],
  },
  {
    icon: "mdi:shield-check",
    title: "可靠保障",
    description: "全方位的质量和安全保障",
    features: ["质量认证", "安全可靠", "售后保障"],
  },
  {
    icon: "mdi:chart-line-variant",
    title: "显著效果",
    description: "帮助客户实现数字化转型",
    features: ["效率提升", "成本降低", "智能决策"],
  },
]);

const featuredProducts = ref([
  {
    icon: "mdi:flood",
    title: '防汛"四预"产品',
    description:
      "智能化防汛决策应用产品，通过实时监测雨水情，提前预测洪水风险，第一时间发出警报，模拟多种洪水影响和处置方案，滚动更新应急响应方案",
    badge: "核心产品",
    tags: ["多元数据融合", "智算模型", "AI大模型"],
  },
  {
    icon: "mdi:water-pump",
    title: "水资源调配产品",
    description:
      "聚焦实施国家节水行动、落实水资源刚性约束的要求，围绕取用水监管服务、水资源综合调度、节水管理等方面建设场景化协同应用",
    badge: "核心产品",
    tags: ["水资源数字账本", "优化配置", "节水管理"],
  },
  {
    icon: "mdi:robot",
    title: "河湖智能助手产品",
    description:
      "智能化河湖综合管护应用产品，通过汇聚空天地水工立体监测数据，动态构建河湖知识库，结合智能模型的多模态处理能力",
    badge: "AI助手",
    tags: ["河湖大百科", "立体感知", "智能模型"],
  },
]);

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);
});

// 事件处理
const handleExplore = () => {
  router.push("/solutions");
};

const handleContact = () => {
  showContactModal.value = true;
};

const handleProducts = () => {
  router.push("/products");
};
</script>

<style lang="scss" scoped>
@import url("@/assets/styles/home.scss");
</style>
