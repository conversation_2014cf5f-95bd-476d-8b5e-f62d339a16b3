@use "./variables.scss" as *;

.solutions {
  min-height: 100vh;
  color: var(--gray-800);
  overflow-x: hidden;

  // 页面加载动画
  opacity: 0;
  transition: opacity var(--duration-700) var(--ease-out);

  &.loaded {
    opacity: 1;

    .fade-in-up {
      opacity: 1;
      transform: translateY(0);

      &.delay-1 {
        animation-delay: 0.1s;
      }
      &.delay-2 {
        animation-delay: 0.2s;
      }
      &.delay-3 {
        animation-delay: 0.3s;
      }
      &.delay-4 {
        animation-delay: 0.4s;
      }
    }
  }
}

// 通用动画类
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s var(--ease-smooth) forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
}

// 英雄区域
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-depth);
    z-index: -1;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
    z-index: -1;
  }

  .hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: var(--electric-blue);
      border-radius: 50%;
      opacity: 0.6;

      &.particle-1 {
        top: 20%;
        left: 10%;
        animation: float 6s infinite 0s;
      }
      &.particle-2 {
        top: 30%;
        left: 80%;
        animation: float 8s infinite 1s;
      }
      &.particle-3 {
        top: 60%;
        left: 15%;
        animation: float 7s infinite 2s;
      }
      &.particle-4 {
        top: 80%;
        left: 70%;
        animation: float 9s infinite 3s;
      }
      &.particle-5 {
        top: 40%;
        left: 90%;
        animation: float 6s infinite 4s;
      }
      &.particle-6 {
        top: 70%;
        left: 25%;
        animation: float 8s infinite 5s;
      }
      &.particle-7 {
        top: 15%;
        left: 60%;
        animation: float 7s infinite 1.5s;
      }
      &.particle-8 {
        top: 85%;
        left: 40%;
        animation: float 9s infinite 2.5s;
      }
      &.particle-9 {
        top: 50%;
        left: 5%;
        animation: float 6s infinite 3.5s;
      }
      &.particle-10 {
        top: 25%;
        left: 95%;
        animation: float 8s infinite 4.5s;
      }
      &.particle-11 {
        top: 75%;
        left: 55%;
        animation: float 7s infinite 0.5s;
      }
      &.particle-12 {
        top: 35%;
        left: 30%;
        animation: float 9s infinite 1.8s;
      }
      &.particle-13 {
        top: 65%;
        left: 85%;
        animation: float 6s infinite 2.8s;
      }
      &.particle-14 {
        top: 45%;
        left: 75%;
        animation: float 8s infinite 3.8s;
      }
      &.particle-15 {
        top: 55%;
        left: 45%;
        animation: float 7s infinite 4.8s;
      }
    }
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.6;
    }
    25% {
      transform: translateY(-20px) rotate(90deg);
      opacity: 1;
    }
    50% {
      transform: translateY(-10px) rotate(180deg);
      opacity: 0.8;
    }
    75% {
      transform: translateY(-30px) rotate(270deg);
      opacity: 1;
    }
  }

  .hero-network {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .network-node {
      position: absolute;
      width: 8px;
      height: 8px;
      background: var(--neon-cyan);
      border-radius: 50%;
      box-shadow: 0 0 20px var(--neon-cyan);

      &.node-1 {
        top: 25%;
        left: 20%;
        animation: pulse 3s infinite 0s;
      }
      &.node-2 {
        top: 40%;
        left: 75%;
        animation: pulse 3s infinite 0.5s;
      }
      &.node-3 {
        top: 65%;
        left: 30%;
        animation: pulse 3s infinite 1s;
      }
      &.node-4 {
        top: 80%;
        left: 80%;
        animation: pulse 3s infinite 1.5s;
      }
      &.node-5 {
        top: 15%;
        left: 85%;
        animation: pulse 3s infinite 2s;
      }
      &.node-6 {
        top: 55%;
        left: 10%;
        animation: pulse 3s infinite 2.5s;
      }
      &.node-7 {
        top: 35%;
        left: 50%;
        animation: pulse 3s infinite 1.2s;
      }
      &.node-8 {
        top: 70%;
        left: 65%;
        animation: pulse 3s infinite 1.8s;
      }
    }

    .network-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.3;

      .network-path {
        fill: none;
        stroke: var(--electric-blue);
        stroke-width: 0.5;
        stroke-dasharray: 5, 5;
        animation: dash 20s linear infinite;
      }
    }
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.4;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.5);
    }
  }

  @keyframes dash {
    to {
      stroke-dashoffset: -100;
    }
  }

  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }
  }

  .hero-content {
    color: var(--white);

    .hero-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--white-alpha-20);
      border: 1px solid var(--white-alpha-30);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      margin-bottom: var(--space-6);
      @include glass-morphism(0.1, 16px, 0.2);

      .badge-icon {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .hero-title {
      font-size: var(--text-5xl);
      font-weight: var(--font-bold);
      line-height: var(--leading-tight);
      margin-bottom: var(--space-6);

      @media (max-width: 768px) {
        font-size: var(--text-4xl);
      }

      .title-line {
        display: block;

        &.title-highlight {
          @include gradient-text(var(--gradient-neon));
        }
      }
    }

    .hero-subtitle {
      font-size: var(--text-lg);
      line-height: var(--leading-relaxed);
      opacity: 0.9;
      margin-bottom: var(--space-8);

      strong {
        color: var(--electric-blue);
        font-weight: var(--font-bold);
      }
    }

    .hero-features {
      display: flex;
      gap: var(--space-8);
      margin-bottom: var(--space-8);

      @media (max-width: 640px) {
        flex-direction: column;
        gap: var(--space-4);
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: var(--space-3);

        .feature-icon {
          width: 50px;
          height: 50px;
          background: var(--gradient-glass);
          @include glass-morphism(0.2, 12px, 0.3);
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--electric-blue);
          font-size: var(--text-xl);
        }

        .feature-text {
          .feature-title {
            font-size: var(--text-base);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-1);
          }

          .feature-desc {
            font-size: var(--text-sm);
            opacity: 0.8;
          }
        }
      }
    }
  }

  .hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    .visual-container {
      position: relative;
      width: 400px;
      height: 400px;

      @media (max-width: 768px) {
        width: 300px;
        height: 300px;
      }
    }

    .solution-hub {
      position: relative;
      width: 100%;
      height: 100%;

      .hub-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100px;
        height: 100px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--glow-primary);
        z-index: 3;

        .hub-icon {
          font-size: var(--text-4xl);
          color: var(--white);
        }

        .hub-pulse {
          position: absolute;
          top: -20px;
          left: -20px;
          width: 140px;
          height: 140px;
          border: 2px solid var(--electric-blue);
          border-radius: 50%;
          animation: hubPulse 3s infinite;
        }
      }

      .hub-orbits {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .orbit {
          position: absolute;
          top: 50%;
          left: 50%;
          border: 1px solid var(--primary-alpha-30);
          border-radius: 50%;
          transform: translate(-50%, -50%);

          &.orbit-1 {
            width: 180px;
            height: 180px;
            animation: rotate 20s linear infinite;
          }

          &.orbit-2 {
            width: 260px;
            height: 260px;
            animation: rotate 30s linear infinite reverse;
          }

          &.orbit-3 {
            width: 340px;
            height: 340px;
            animation: rotate 40s linear infinite;
          }

          .orbit-item {
            position: absolute;
            width: 40px;
            height: 40px;
            background: var(--gradient-glass);
            @include glass-morphism(0.2, 12px, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--electric-blue);
            font-size: var(--text-lg);
            box-shadow: var(--glow-neon);

            &.item-1 {
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-2 {
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-3 {
              bottom: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-4 {
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-5 {
              right: -20px;
              top: 50%;
              transform: translateY(-50%);
            }

            &.item-6 {
              bottom: -20px;
              left: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }
    }
  }

  @keyframes hubPulse {
    0%,
    100% {
      opacity: 0.5;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 0.2;
      transform: translate(-50%, -50%) scale(1.2);
    }
  }

  @keyframes rotate {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }
}

// 行业痛点区域
.industry-pain-points {
  @include section-padding(var(--space-20), var(--space-20));
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;

  // 简化的背景装饰
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        circle at 30% 70%,
        rgba(59, 130, 246, 0.03) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 70% 30%,
        rgba(139, 92, 246, 0.03) 0%,
        transparent 50%
      );
    z-index: 0;
    pointer-events: none;
  }

  .container {
    position: relative;
    z-index: 1;
  }

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-3) var(--space-5);
      background: linear-gradient(
        135deg,
        rgba(239, 68, 68, 0.1) 0%,
        rgba(245, 101, 101, 0.1) 100%
      );
      border: 2px solid rgba(239, 68, 68, 0.2);
      border-radius: var(--radius-3xl);
      font-size: var(--text-sm);
      font-weight: var(--font-bold);
      color: #dc2626;
      margin-bottom: var(--space-8);
      backdrop-filter: blur(10px);
      box-shadow: 0 8px 32px rgba(239, 68, 68, 0.1);
      transition: var(--transition-all);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(239, 68, 68, 0.15);
      }

      svg {
        font-size: var(--text-lg);
        color: #dc2626;
        filter: drop-shadow(0 2px 4px rgba(239, 68, 68, 0.2));
      }
    }

    .section-title {
      font-size: var(--text-5xl);
      font-weight: var(--font-black);
      margin-bottom: var(--space-6);
      background: linear-gradient(
        135deg,
        #1e293b 0%,
        #475569 50%,
        #64748b 100%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      letter-spacing: -0.02em;

      @media (max-width: 768px) {
        font-size: var(--text-4xl);
      }
    }

    .section-subtitle {
      font-size: var(--text-xl);
      color: var(--gray-600);
      font-weight: var(--font-medium);
      max-width: 600px;
      margin: 0 auto;
      line-height: var(--leading-relaxed);
    }
  }

  .pain-points-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: var(--space-6);
    }
  }

  .pain-point-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-3xl);
    padding: var(--space-10);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    will-change: transform;

    // 简化的装饰线条
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ef4444, #f59e0b);
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);

      &::before {
        opacity: 1;
      }

      .point-icon {
        transform: translateY(-2px);
      }

      .point-title {
        color: var(--primary-blue);
      }
    }

    .point-icon {
      width: 90px;
      height: 90px;
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--space-8);
      color: var(--white);
      font-size: var(--text-3xl);
      box-shadow: 0 8px 24px rgba(239, 68, 68, 0.15);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      position: relative;
      will-change: transform;

      // 简化的内部光效
      &::before {
        content: "";
        position: absolute;
        top: 25%;
        left: 25%;
        width: 50%;
        height: 50%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.2) 0%,
          transparent 60%
        );
        border-radius: 50%;
      }
    }

    .point-title {
      font-size: var(--text-2xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-5);
      transition: color 0.2s ease;
      letter-spacing: -0.01em;
    }

    .point-description {
      color: var(--gray-600);
      line-height: var(--leading-relaxed);
      font-size: var(--text-base);
      font-weight: var(--font-medium);
    }

    // 不同卡片的特色颜色 - 简化版本
    &:nth-child(1) .point-icon {
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15);
    }

    &:nth-child(2) .point-icon {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      box-shadow: 0 8px 24px rgba(245, 158, 11, 0.15);
    }

    &:nth-child(3) .point-icon {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      box-shadow: 0 8px 24px rgba(239, 68, 68, 0.15);
    }

    &:nth-child(4) .point-icon {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      box-shadow: 0 8px 24px rgba(139, 92, 246, 0.15);
    }
  }
}

// 方案特点区域
.solution-features {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .features-grid {
    @include responsive-grid(auto-fit, 300px, var(--space-8));
  }

  .feature-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;

    @include card-hover(1.02, -6px);

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: var(--feature-color);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      background: var(--feature-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--space-6);
      color: var(--white);
      font-size: var(--text-3xl);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .feature-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
    }

    .feature-description {
      color: var(--gray-600);
      line-height: var(--leading-relaxed);
      font-size: var(--text-sm);
    }
  }
}

// 核心解决方案区域
.core-solutions {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .solutions-grid {
    @include responsive-grid(auto-fit, 380px, var(--space-8));
  }

  .solution-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);

    @include card-hover(1.02, -6px);

    .card-header {
      position: relative;
      padding: var(--space-8);
      background: var(--gradient-subtle);
      text-align: center;

      .solution-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-4);
        color: var(--white);
        font-size: var(--text-3xl);
        box-shadow: var(--glow-primary);
        transition: var(--transition-all);
      }

      .solution-category {
        display: inline-block;
        padding: var(--space-1) var(--space-3);
        background: var(--primary-alpha-20);
        color: var(--primary-blue);
        border-radius: var(--radius-2xl);
        font-size: var(--text-xs);
        font-weight: var(--font-bold);
      }
    }

    .card-content {
      padding: var(--space-6);

      .solution-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
        line-height: var(--leading-tight);
      }

      .solution-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-6);
        font-size: var(--text-sm);
      }

      .solution-benefits {
        margin-bottom: var(--space-6);

        .benefits-title {
          font-size: var(--text-sm);
          font-weight: var(--font-bold);
          color: var(--gray-800);
          margin-bottom: var(--space-3);
        }

        .benefits-list {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);

          .benefit {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
            color: var(--gray-700);

            .benefit-icon {
              color: var(--success-green);
              font-size: var(--text-sm);
              flex-shrink: 0;
            }
          }
        }
      }

      .solution-tech {
        margin-bottom: var(--space-6);

        .tech-title {
          font-size: var(--text-sm);
          font-weight: var(--font-bold);
          color: var(--gray-800);
          margin-bottom: var(--space-3);
        }

        .tech-tags {
          display: flex;
          flex-wrap: wrap;
          gap: var(--space-2);

          .tech-tag {
            background: var(--crystal-blue);
            color: var(--primary-blue);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-2xl);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
          }
        }
      }
    }

    &:hover {
      .card-header .solution-icon {
        transform: scale(1.1);
      }
    }
  }
}

// 应用场景区域
.application-scenarios {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--white);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .scenarios-grid {
    @include responsive-grid(auto-fit, 280px, var(--space-8));
  }

  .scenario-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;

    @include card-hover(1.02, -6px);

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: var(--scenario-color);
    }

    .scenario-icon {
      width: 80px;
      height: 80px;
      background: var(--scenario-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--space-6);
      color: var(--white);
      font-size: var(--text-3xl);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .scenario-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
    }

    .scenario-description {
      color: var(--gray-600);
      line-height: var(--leading-relaxed);
      font-size: var(--text-sm);
    }
  }
}

// CTA 区域
.cta-section {
  position: relative;
  @include section-padding(var(--space-20), var(--space-20));
  overflow: hidden;

  .cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
  }

  .cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
  }

  .cta-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(5, 1fr);
    opacity: 0.1;

    .grid-item {
      border: 1px solid var(--white-alpha-20);
      transition: var(--transition-all);

      &:hover {
        background: var(--white-alpha-10);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-12);
    align-items: center;
    color: var(--white);

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: var(--space-8);
    }

    .cta-text {
      .cta-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: var(--white-alpha-20);
        border: 1px solid var(--white-alpha-30);
        border-radius: var(--radius-2xl);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        margin-bottom: var(--space-6);
        @include glass-morphism(0.1, 16px, 0.2);

        svg {
          font-size: var(--text-lg);
          color: var(--electric-blue);
        }
      }

      .cta-title {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        margin-bottom: var(--space-6);
        line-height: var(--leading-tight);

        @media (max-width: 768px) {
          font-size: var(--text-3xl);
        }
      }

      .cta-subtitle {
        font-size: var(--text-lg);
        line-height: var(--leading-relaxed);
        opacity: 0.9;
        margin-bottom: var(--space-8);

        strong {
          color: var(--electric-blue);
          font-weight: var(--font-bold);
        }
      }

      .cta-benefits {
        display: flex;
        gap: var(--space-6);
        margin-bottom: var(--space-8);

        @media (max-width: 640px) {
          flex-direction: column;
          gap: var(--space-4);
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);

          svg {
            font-size: var(--text-lg);
            color: var(--electric-blue);
          }
        }
      }
    }

  }
}

// 响应式适配
@media (max-width: 968px) {
  .hero-section {
    min-height: 80vh;

    .hero-title {
      font-size: var(--text-4xl);
    }

    .hero-subtitle {
      font-size: var(--text-base);
    }

    .hero-features {
      flex-direction: column;
      gap: var(--space-4);
    }
  }

  .core-solutions,
  .solution-features,
  .application-scenarios {
    .solutions-grid,
    .features-grid,
    .scenarios-grid {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 640px) {
  .hero-section {
    .hero-title {
      font-size: var(--text-3xl);
    }

    .hero-visual .visual-container {
      width: 250px;
      height: 250px;
    }
  }

  .core-solutions,
  .solution-features,
  .application-scenarios,
  .cta-section {
    .section-header .section-title {
      font-size: var(--text-3xl);
    }
  }
}