<template>
  <!-- 联系我们弹窗 -->
  <Teleport to="body">
    <div
      class="contact-modal-overlay"
      :class="{ active: modelValue }"
      @click="handleClose"
    >
      <div class="contact-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <Icon icon="mdi:phone" class="title-icon" />
            联系我们
          </h3>
          <button class="close-btn" @click="handleClose">
            <Icon icon="mdi:close" />
          </button>
        </div>

        <div class="modal-content">
          <div class="contact-info-grid">
            <div class="contact-item">
              <div class="contact-icon">
                <Icon icon="mdi:map-marker" />
              </div>
              <div class="contact-details">
                <h4>公司总部</h4>
                <p>北京市海淀区中关村科技园</p>
              </div>
            </div>

            <div class="contact-item">
              <div class="contact-icon">
                <Icon icon="mdi:cog" />
              </div>
              <div class="contact-details">
                <h4>研发中心</h4>
                <p>江苏省无锡市滨湖区雪浪小镇</p>
              </div>
            </div>

            <div class="contact-item">
              <div class="contact-icon">
                <Icon icon="mdi:email" />
              </div>
              <div class="contact-details">
                <h4>电子邮箱</h4>
                <p>{{ contactInfo.email }}</p>
              </div>
            </div>

            <div class="contact-item">
              <div class="contact-icon">
                <Icon icon="mdi:phone" />
              </div>
              <div class="contact-details">
                <h4>联系电话</h4>
                <p>{{ contactInfo.phone }}</p>
              </div>
            </div>
          </div>

          <div class="contact-actions">
            <button class="action-btn primary" @click="copyEmail">
              <Icon icon="mdi:content-copy" />
              复制邮箱
            </button>
            <button class="action-btn secondary" @click="copyPhone">
              <Icon icon="mdi:content-copy" />
              复制电话
            </button>
          </div>

          <div v-if="copyMessage" class="copy-message">
            <Icon icon="mdi:check-circle" />
            {{ copyMessage }}
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { Icon } from "@iconify/vue";
import { ref, watch } from "vue";

defineOptions({
  name: "ContactModal",
});

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["update:modelValue"]);

// 联系信息配置
const contactInfo = {
  email: "<EMAIL>",
  phone: "186-1032-4200",
};

// 复制提示消息
const copyMessage = ref("");

// 处理关闭弹窗
const handleClose = () => {
  emit("update:modelValue", false);
};

// 复制邮箱
const copyEmail = async () => {
  try {
    await navigator.clipboard.writeText(contactInfo.email);
    showCopyMessage("邮箱已复制到剪贴板");
  } catch (err) {
    console.error("复制失败:", err);
    showCopyMessage("复制失败，请手动复制");
  }
};

// 复制电话
const copyPhone = async () => {
  try {
    await navigator.clipboard.writeText(contactInfo.phone);
    showCopyMessage("电话已复制到剪贴板");
  } catch (err) {
    console.error("复制失败:", err);
    showCopyMessage("复制失败，请手动复制");
  }
};

// 显示复制提示
const showCopyMessage = (message) => {
  copyMessage.value = message;
  setTimeout(() => {
    copyMessage.value = "";
  }, 2000);
};

// 监听弹窗状态，控制页面滚动
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
      copyMessage.value = ""; // 关闭时清除提示消息
    }
  }
);

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === "Escape" && props.modelValue) {
    handleClose();
  }
};

// 组件挂载时添加键盘监听
import { onMounted, onUnmounted } from "vue";

onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
  document.body.style.overflow = ""; // 清理样式
});
</script>

<style lang="scss" scoped>
@use "@/assets/styles/variables.scss" as *;

// 联系弹窗
.contact-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: var(--dark-alpha-70);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-all);
  z-index: var(--z-modal);
  padding: var(--space-4);

  &.active {
    opacity: 1;
    visibility: visible;

    .contact-modal {
      transform: scale(1) translateY(0);
    }
  }

  .contact-modal {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: var(--transition-all);

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--space-6) var(--space-6) var(--space-4);
      border-bottom: 1px solid var(--gray-200);

      .modal-title {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin: 0;

        .title-icon {
          font-size: var(--text-2xl);
          color: var(--primary-blue);
        }
      }

      .close-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border: none;
        background: var(--gray-100);
        border-radius: var(--radius-full);
        color: var(--gray-600);
        cursor: pointer;
        transition: var(--transition-all);

        &:hover {
          background: var(--gray-200);
          color: var(--gray-800);
        }

        svg {
          font-size: var(--text-xl);
        }
      }
    }

    .modal-content {
      padding: var(--space-6);

      .contact-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-4);
        margin-bottom: var(--space-6);

        .contact-item {
          display: flex;
          align-items: flex-start;
          gap: var(--space-4);
          padding: var(--space-4);
          background: var(--gray-50);
          border-radius: var(--radius-xl);
          border: 1px solid var(--gray-200);
          transition: var(--transition-all);

          &:hover {
            background: var(--primary-alpha-5);
            border-color: var(--primary-200);
          }

          .contact-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            background: var(--gradient-primary);
            border-radius: var(--radius-xl);
            color: var(--white);
            flex-shrink: 0;

            svg {
              font-size: var(--text-xl);
            }
          }

          .contact-details {
            flex: 1;

            h4 {
              font-size: var(--text-base);
              font-weight: var(--font-semibold);
              color: var(--gray-900);
              margin: 0 0 var(--space-1);
            }

            p {
              font-size: var(--text-sm);
              color: var(--gray-600);
              margin: 0;
              line-height: var(--leading-relaxed);
            }
          }
        }
      }

      .contact-actions {
        display: flex;
        gap: var(--space-3);
        justify-content: center;
        margin-bottom: var(--space-4);

        .action-btn {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          padding: var(--space-3) var(--space-5);
          border: none;
          border-radius: var(--radius-xl);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          cursor: pointer;
          transition: var(--transition-all);

          &.primary {
            background: var(--gradient-primary);
            color: var(--white);

            &:hover {
              transform: translateY(-2px);
              box-shadow: var(--shadow-lg);
            }
          }

          &.secondary {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 1px solid var(--gray-300);

            &:hover {
              background: var(--gray-200);
              transform: translateY(-2px);
            }
          }

          svg {
            font-size: var(--text-base);
          }
        }
      }

      .copy-message {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-2);
        padding: var(--space-3);
        background: var(--green-50);
        border: 1px solid var(--green-200);
        border-radius: var(--radius-lg);
        color: var(--success-green);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);

        svg {
          color: var(--green-600);
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .contact-modal-overlay {
    padding: var(--space-2);

    .contact-modal {
      .modal-content {
        padding: var(--space-4);

        .contact-info-grid {
          grid-template-columns: 1fr;
          gap: var(--space-3);
        }

        .contact-actions {
          flex-direction: column;

          .action-btn {
            width: 100%;
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
