<template>
  <nav class="navbar" :class="{ scrolled: isScrolled }">
    <div class="container">
      <!-- Logo 区域 -->
      <div class="logo">
        <router-link to="/">
          <div class="logo-wrapper">
            <img
              src="@/assets/images/logo.webp"
              alt="怀川科技"
              class="logo-img"
            />
            <div class="logo-text-group">
              <img
                src="@/assets/images/title.webp"
                alt="怀川科技"
                class="logo-text-img"
              />
            </div>
          </div>
        </router-link>
      </div>

      <!-- 主导航菜单 -->
      <div class="menu" :class="{ 'mobile-active': mobileMenuOpen }">
        <ul class="nav-list">
          <li class="nav-item">
            <router-link to="/products" class="nav-link">
              <div class="nav-content">
                <Icon icon="mdi:package-variant-closed" class="nav-icon" />
                <span class="nav-text">产品中心</span>
              </div>
              <div class="nav-indicator"></div>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/solutions" class="nav-link">
              <div class="nav-content">
                <Icon icon="mdi:lightbulb-on-outline" class="nav-icon" />
                <span class="nav-text">解决方案</span>
              </div>
              <div class="nav-indicator"></div>
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/about" class="nav-link">
              <div class="nav-content">
                <Icon icon="mdi:information-outline" class="nav-icon" />
                <span class="nav-text">关于我们</span>
              </div>
              <div class="nav-indicator"></div>
            </router-link>
          </li>
        </ul>

        <!-- CTA 按钮 -->
        <div class="nav-cta">
          <button class="contact-btn" @click="handleContact">
            <Icon icon="mdi:phone" class="btn-icon" />
            <span>联系我们</span>
          </button>
        </div>
      </div>

      <!-- 移动端菜单按钮 -->
      <button
        class="mobile-toggle"
        :class="{ active: mobileMenuOpen }"
        @click="toggleMobileMenu"
        aria-label="切换菜单"
      >
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>
    </div>

    <!-- 移动端菜单遮罩 -->
    <div
      class="mobile-overlay"
      :class="{ active: mobileMenuOpen }"
      @click="closeMobileMenu"
    ></div>

    <!-- 联系我们弹窗 -->
    <ContactModal v-model="showContactModal" />
  </nav>
</template>

<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted, onUnmounted } from "vue";
import ContactModal from "./ContactModal.vue";

defineOptions({
  name: "NavBar",
});

const isScrolled = ref(false);
const mobileMenuOpen = ref(false);
const showContactModal = ref(false);

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50;
};

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
  // 防止滚动穿透
  document.body.style.overflow = mobileMenuOpen.value ? "hidden" : "";
};

const closeMobileMenu = () => {
  mobileMenuOpen.value = false;
  document.body.style.overflow = "";
};

const handleContact = () => {
  // 关闭移动端菜单（如果打开的话）
  if (mobileMenuOpen.value) {
    closeMobileMenu();
  }

  // 显示联系弹窗
  showContactModal.value = true;
};

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
  handleScroll(); // 初始检查
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
  document.body.style.overflow = ""; // 清理样式
});
</script>

<style lang="scss" scoped>
@use "@/assets/styles/variables.scss" as *;

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  z-index: var(--z-sticky);
  transition: var(--transition-all);

  // 默认状态 - 透明玻璃效果
  @include blue-glass(0.08, 20px);
  border-bottom: 1px solid var(--primary-alpha-10);

  // 滚动后状态 - 更强的背景
  &.scrolled {
    @include blue-glass(0.15, 24px);
    border-bottom: 1px solid var(--primary-alpha-20);
    box-shadow: var(--shadow-lg);
    height: 70px;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-6);
    position: relative;
  }

  // Logo 区域设计
  .logo {
    z-index: var(--z-docked);

    a {
      display: block;
      text-decoration: none;
      transition: var(--transition-all);

      &:hover {
        transform: translateY(-1px);
      }
    }

    .logo-wrapper {
      display: flex;
      align-items: center;
      gap: var(--space-3);
    }

    .logo-img {
      height: 48px;
      width: auto;
      transition: var(--transition-all);
      filter: drop-shadow(0 2px 8px var(--primary-alpha-30));

      .scrolled & {
        height: 42px;
      }

      &:hover {
        transform: scale(1.05);
        filter: drop-shadow(0 4px 12px var(--primary-alpha-40));
      }
    }

    .logo-text-group {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .logo-text-img {
      height: 38px;
      width: auto;
      object-fit: contain;
      transition: var(--transition-all);

      .scrolled & {
        height: 40px;
      }
    }
  }

  // 主导航菜单
  .menu {
    display: flex;
    align-items: center;
    gap: var(--space-8);

    .nav-list {
      display: flex;
      align-items: center;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: var(--space-2);
    }

    .nav-item {
      position: relative;
    }

    .nav-link {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--space-3) var(--space-4);
      border-radius: var(--radius-xl);
      text-decoration: none;
      transition: var(--transition-all);
      color: var(--gray-500);
      font-weight: var(--font-medium);
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-subtle);
        opacity: 0;
        transition: var(--transition-all);
        border-radius: var(--radius-xl);
      }

      &:hover {
        color: var(--primary-blue);
        transform: translateY(-2px);

        &::before {
          opacity: 1;
        }

        .nav-icon {
          color: var(--tech-blue);
          transform: scale(1.1);
        }

        .nav-indicator {
          transform: scaleX(1);
        }
      }

      &.router-link-active {
        color: var(--primary-blue);
        background: var(--crystal-blue);

        .nav-icon {
          color: var(--tech-blue);
        }

        .nav-indicator {
          transform: scaleX(1);
          background: var(--gradient-primary);
        }
      }
    }

    .nav-content {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      position: relative;
      z-index: 2;
    }

    .nav-icon {
      font-size: var(--text-lg);
      transition: var(--transition-all);
    }

    .nav-text {
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      white-space: nowrap;
    }

    .nav-indicator {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 80%;
      height: 2px;
      background: var(--gradient-ocean);
      border-radius: var(--radius-full);
      transform: translateX(-50%) scaleX(0);
      transition: var(--transition-all);
      opacity: 0.8;
    }
  }

  // CTA 按钮
  .nav-cta {
    .contact-btn {
      @include button-primary(var(--gradient-primary));
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2-5) var(--space-5);
      font-size: var(--text-sm);
      border-radius: var(--radius-2xl);
      white-space: nowrap;

      .btn-icon {
        font-size: var(--text-base);
      }
    }
  }

  // 移动端菜单按钮
  .mobile-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background: none;
    border: none;
    cursor: pointer;
    z-index: var(--z-modal);
    border-radius: var(--radius-lg);
    transition: var(--transition-all);

    &:hover {
      background: var(--primary-alpha-10);
    }

    .hamburger-line {
      width: 24px;
      height: 2px;
      background: var(--primary-blue);
      margin: 3px 0;
      transition: var(--transition-all);
      border-radius: var(--radius-full);
    }

    &.active {
      .hamburger-line {
        &:nth-child(1) {
          transform: rotate(45deg) translate(6px, 6px);
        }

        &:nth-child(2) {
          opacity: 0;
        }

        &:nth-child(3) {
          transform: rotate(-45deg) translate(6px, -6px);
        }
      }
    }
  }

  // 移动端遮罩
  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--dark-alpha-60);
    backdrop-filter: blur(4px);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-all);
    z-index: var(--z-overlay);

    &.active {
      opacity: 1;
      visibility: visible;
    }
  }



  // 平板适配
  @media (max-width: 1024px) {
    .container {
      padding: 0 var(--space-4);
    }

    .menu {
      gap: var(--space-6);

      .nav-list {
        gap: var(--space-1);
      }

      .nav-link {
        padding: var(--space-2) var(--space-3);
      }

      .nav-text {
        font-size: var(--text-xs);
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    height: 70px;

    &.scrolled {
      height: 65px;
    }



    .logo {
      .logo-img {
        height: 40px;

        .scrolled & {
          height: 36px;
        }
      }

      .logo-text-img {
        height: 40px;

        .scrolled & {
          height: 32px;
        }
      }
    }

    .mobile-toggle {
      display: flex;
    }

    .menu {
      position: fixed;
      top: 0;
      right: -100%;
      width: 280px;
      height: 100vh;
      background: var(--white);
      flex-direction: column;
      justify-content: flex-start;
      align-items: stretch;
      padding: var(--space-20) var(--space-6) var(--space-6);
      box-shadow: var(--shadow-2xl);
      transition: var(--transition-all);
      z-index: var(--z-modal);

      &.mobile-active {
        right: 0;
      }

      .nav-list {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-2);
        margin-bottom: var(--space-8);
      }

      .nav-link {
        justify-content: flex-start;
        padding: var(--space-4) var(--space-4);
        color: var(--gray-800);
        border-radius: var(--radius-lg);

        &:hover {
          background: var(--crystal-blue);
          transform: translateX(4px);
        }

        &.router-link-active {
          background: var(--primary-alpha-10);
          color: var(--primary-blue);
        }
      }

      .nav-content {
        gap: var(--space-3);
      }

      .nav-icon {
        font-size: var(--text-xl);
      }

      .nav-text {
        font-size: var(--text-base);
      }

      .nav-indicator {
        display: none;
      }

      .nav-cta {
        .contact-btn {
          width: 100%;
          justify-content: center;
          padding: var(--space-4) var(--space-6);
          font-size: var(--text-base);
        }
      }
    }
  }

  // 小屏幕移动端
  @media (max-width: 480px) {
    .container {
      padding: 0 var(--space-3);
    }

    .menu {
      width: calc(100% - var(--space-6));
      right: -100%;

      &.mobile-active {
        right: var(--space-3);
      }
    }
  }
}
</style>
